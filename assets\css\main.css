/**
 * Huval - AI Evaluation Platform
 * Main CSS file
 */

/* Import design system */
@import '../../design-system/variables.css';
@import '../../design-system/colors.css';
@import '../../design-system/typography.css';
@import '../../design-system/components.css';

/* Base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--color-background);
  color: var(--color-text);
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

img {
  max-width: 100%;
  height: auto;
}

/* Header styles */
.site-header {
  position: sticky;
  top: 0;
  background-color: var(--color-background-card);
  box-shadow: var(--shadow-sm);
  z-index: var(--z-index-30);
  height: var(--header-height);
  display: flex;
  align-items: center;
}

.site-header-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.site-logo {
  display: flex;
  align-items: center;
}

.site-logo img, .site-logo svg {
  height: 2rem;
  width: auto;
}

.site-nav {
  display: flex;
  align-items: center;
}

.site-nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.site-nav-item {
  margin: 0 var(--space-1);
}

.site-nav-link {
  display: block;
  padding: var(--nav-item-padding-y) var(--nav-item-padding-x);
  color: var(--color-text);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
}

.site-nav-link:hover {
  color: var(--color-primary);
  text-decoration: none;
}

.site-nav-link.active {
  color: var(--color-primary);
}

.auth-buttons {
  display: flex;
  gap: var(--space-2);
  margin-left: var(--space-4);
}

/* Mobile menu */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--color-text);
  font-size: 1.5rem;
  cursor: pointer;
}

@media (max-width: 768px) {
  .mobile-menu-toggle {
    display: block;
  }
  
  .site-nav {
    position: fixed;
    top: var(--header-height);
    left: 0;
    width: 100%;
    height: calc(100vh - var(--header-height));
    background-color: var(--color-background-card);
    flex-direction: column;
    align-items: flex-start;
    padding: var(--space-4);
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
    z-index: var(--z-index-20);
  }
  
  .site-nav.open {
    transform: translateX(0);
  }
  
  .site-nav-list {
    flex-direction: column;
    width: 100%;
  }
  
  .site-nav-item {
    margin: var(--space-2) 0;
    width: 100%;
  }
  
  .site-nav-link {
    padding: var(--space-3) 0;
  }
  
  .auth-buttons {
    margin-left: 0;
    margin-top: var(--space-4);
    width: 100%;
  }
  
  .auth-buttons .btn {
    flex: 1;
  }
}

/* Footer styles */
.site-footer {
  background-color: var(--color-neutral-800);
  color: var(--color-neutral-300);
  padding: var(--footer-padding-y) 0;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-8);
}

.footer-column h4 {
  color: var(--color-neutral-100);
  margin-bottom: var(--space-4);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: var(--space-2);
}

.footer-links a {
  color: var(--color-neutral-400);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--color-neutral-100);
  text-decoration: none;
}

.footer-bottom {
  margin-top: var(--space-8);
  padding-top: var(--space-4);
  border-top: 1px solid var(--color-neutral-700);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.footer-copyright {
  color: var(--color-neutral-500);
  font-size: var(--font-size-sm);
}

.footer-social {
  display: flex;
  gap: var(--space-3);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-full);
  background-color: var(--color-neutral-700);
  color: var(--color-neutral-300);
  transition: all var(--transition-fast);
}

.social-link:hover {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

/* Hero section */
.hero {
  padding: var(--space-16) 0;
  background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  color: var(--color-text-inverse);
  position: relative;
  overflow: hidden;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.5;
}

.hero-content {
  position: relative;
  z-index: 1;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-4);
  line-height: 1.2;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-8);
  opacity: 0.9;
}

.hero-buttons {
  display: flex;
  gap: var(--space-4);
  justify-content: center;
}

@media (max-width: 768px) {
  .hero {
    padding: var(--space-10) 0;
  }
  
  .hero-title {
    font-size: var(--font-size-4xl);
  }
  
  .hero-subtitle {
    font-size: var(--font-size-lg);
  }
  
  .hero-buttons {
    flex-direction: column;
    gap: var(--space-3);
  }
}

/* Features section */
.features {
  padding: var(--space-16) 0;
}

.section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto var(--space-12);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-3);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
}

.feature-card {
  text-align: center;
  padding: var(--space-6);
}

.feature-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  border-radius: var(--radius-full);
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--color-primary);
  font-size: 1.5rem;
  margin-bottom: var(--space-4);
}

.feature-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-3);
}

.feature-description {
  color: var(--color-text-secondary);
}

/* How it works section */
.how-it-works {
  padding: var(--space-16) 0;
  background-color: var(--color-background-alt);
}

.steps {
  max-width: 800px;
  margin: 0 auto;
}

.step {
  display: flex;
  margin-bottom: var(--space-8);
  position: relative;
}

.step:last-child {
  margin-bottom: 0;
}

.step:not(:last-child)::after {
  content: "";
  position: absolute;
  top: 3rem;
  left: 1.5rem;
  width: 2px;
  height: calc(100% - 3rem);
  background-color: var(--color-primary-light);
}

.step-number {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-full);
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  font-weight: var(--font-weight-bold);
  margin-right: var(--space-4);
  z-index: 1;
}

.step-content {
  padding-top: 0.5rem;
}

.step-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-2);
}

.step-description {
  color: var(--color-text-secondary);
}

/* Testimonials section */
.testimonials {
  padding: var(--space-16) 0;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
}

.testimonial-card {
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  background-color: var(--color-background-card);
}

.testimonial-content {
  font-style: italic;
  margin-bottom: var(--space-4);
  position: relative;
}

.testimonial-content::before {
  content: "\201C";
  font-size: 4rem;
  position: absolute;
  left: -1rem;
  top: -2rem;
  color: var(--color-primary-light);
  opacity: 0.3;
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.testimonial-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-full);
  margin-right: var(--space-3);
  overflow: hidden;
}

.testimonial-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.testimonial-info {
  flex: 1;
}

.testimonial-name {
  font-weight: var(--font-weight-semibold);
}

.testimonial-role {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* CTA section */
.cta {
  padding: var(--space-16) 0;
  background: linear-gradient(135deg, var(--color-tertiary-dark) 0%, var(--color-tertiary) 100%);
  color: var(--color-text-inverse);
  text-align: center;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-4);
}

.cta-description {
  margin-bottom: var(--space-8);
  opacity: 0.9;
}

/* FAQ section */
.faq {
  padding: var(--space-16) 0;
}

.faq-list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  margin-bottom: var(--space-4);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.faq-question {
  padding: var(--space-4);
  background-color: var(--color-background-card);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.faq-question::after {
  content: "+";
  font-size: 1.5rem;
  transition: transform var(--transition-fast);
}

.faq-item.active .faq-question::after {
  transform: rotate(45deg);
}

.faq-answer {
  padding: 0 var(--space-4);
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal), padding var(--transition-normal);
}

.faq-item.active .faq-answer {
  padding: 0 var(--space-4) var(--space-4);
  max-height: 1000px;
}

/* Topic list styles */
.topic-filters {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background-color: var(--color-background-card);
  border-radius: var(--radius-lg);
}

.topic-search {
  flex: 1;
  min-width: 200px;
  margin-right: var(--space-4);
}

.filter-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  align-items: center;
}

.filter-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-right: var(--space-1);
}

.topic-list {
  margin-top: var(--space-6);
}

.topic-item {
  margin-bottom: var(--space-4);
  border-radius: var(--radius-lg);
  overflow: hidden;
  background-color: var(--color-background-card);
  box-shadow: var(--shadow-sm);
  transition: box-shadow var(--transition-fast);
}

.topic-item:hover {
  box-shadow: var(--shadow-md);
}

/* Single topic page */
.topic-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--color-border);
  margin-bottom: var(--space-6);
}

.topic-meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  margin-top: var(--space-3);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.topic-meta-item {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.topic-content {
  padding: 0 var(--space-6) var(--space-6);
}

.topic-body {
  margin-bottom: var(--space-6);
}

.ai-responses-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-3);
  border-bottom: 1px solid var(--color-border);
}

.ai-responses-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.ai-responses-count {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.ai-responses-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

/* Leaderboard page */
.leaderboard-header {
  margin-bottom: var(--space-6);
}

.leaderboard-filters {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
  padding: var(--space-4);
  background-color: var(--color-background-card);
  border-radius: var(--radius-lg);
}

.leaderboard-table-container {
  overflow-x: auto;
}

.leaderboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.stat-card {
  padding: var(--space-4);
  background-color: var(--color-background-card);
  border-radius: var(--radius-lg);
  text-align: center;
}

.stat-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-1);
  color: var(--color-primary);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Create topic page */
.create-topic-form {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--space-6);
  background-color: var(--color-background-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.form-section {
  margin-bottom: var(--space-6);
}

.form-section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-3);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--color-border);
}

.category-selector {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  margin-top: var(--space-2);
}

.category-option {
  position: relative;
}

.category-option input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.category-option label {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  background-color: var(--color-neutral-100);
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.category-option input:checked + label {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.nsfw-toggle {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-4);
}

/* Auth pages */
.auth-container {
  max-width: 400px;
  margin: var(--space-16) auto;
  padding: var(--space-6);
  background-color: var(--color-background-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.auth-header {
  text-align: center;
  margin-bottom: var(--space-6);
}

.auth-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-2);
}

.auth-subtitle {
  color: var(--color-text-secondary);
}

.auth-form {
  margin-bottom: var(--space-4);
}

.auth-footer {
  text-align: center;
  padding-top: var(--space-4);
  border-top: 1px solid var(--color-border);
  font-size: var(--font-size-sm);
}

.social-auth {
  margin-bottom: var(--space-4);
}

.social-auth-buttons {
  display: flex;
  gap: var(--space-2);
}

.social-auth-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-2);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
  background-color: var(--color-background-card);
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.social-auth-button:hover {
  background-color: var(--color-neutral-100);
}

.auth-divider {
  display: flex;
  align-items: center;
  margin: var(--space-4) 0;
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.auth-divider::before,
.auth-divider::after {
  content: "";
  flex: 1;
  height: 1px;
  background-color: var(--color-border);
}

.auth-divider::before {
  margin-right: var(--space-2);
}

.auth-divider::after {
  margin-left: var(--space-2);
}

/* Profile page */
.profile-header {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-6);
  margin-bottom: var(--space-8);
  padding: var(--space-6);
  background-color: var(--color-background-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.profile-avatar-section {
  flex-shrink: 0;
}

.profile-info {
  flex: 1;
  min-width: 200px;
}

.profile-name-section {
  margin-bottom: var(--space-4);
}

.profile-username {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-2);
}

.profile-role-badge {
  display: inline-block;
  margin-bottom: var(--space-4);
}

.profile-stats {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-6);
  margin-bottom: var(--space-4);
}

.profile-actions {
  display: flex;
  gap: var(--space-2);
}

.profile-tabs {
  margin-bottom: var(--space-6);
}

.profile-content {
  min-height: 300px;
}

/* Settings page */
.settings-container {
  display: grid;
  grid-template-columns: 200px 1fr;
  gap: var(--space-6);
}

.settings-sidebar {
  position: sticky;
  top: calc(var(--header-height) + var(--space-4));
  height: fit-content;
}

.settings-nav {
  background-color: var(--color-background-card);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.settings-nav-item {
  display: block;
  padding: var(--space-3) var(--space-4);
  color: var(--color-text);
  text-decoration: none;
  border-left: 3px solid transparent;
  transition: all var(--transition-fast);
}

.settings-nav-item:hover {
  background-color: var(--color-background-alt);
  text-decoration: none;
}

.settings-nav-item.active {
  background-color: var(--color-background-alt);
  border-left-color: var(--color-primary);
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.settings-content {
  background-color: var(--color-background-card);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
}

.settings-section {
  margin-bottom: var(--space-8);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section-header {
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--color-border);
}

@media (max-width: 768px) {
  .settings-container {
    grid-template-columns: 1fr;
  }
  
  .settings-sidebar {
    position: static;
    margin-bottom: var(--space-4);
  }
  
  .settings-nav {
    display: flex;
    overflow-x: auto;
    white-space: nowrap;
    padding: var(--space-2);
  }
  
  .settings-nav-item {
    border-left: none;
    border-bottom: 3px solid transparent;
  }
  
  .settings-nav-item.active {
    border-left-color: transparent;
    border-bottom-color: var(--color-primary);
  }
}

/* AI Provider dashboard */
.provider-dashboard-header {
  margin-bottom: var(--space-6);
}

.provider-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-8);
}

.provider-models {
  margin-bottom: var(--space-8);
}

.model-card {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  padding: var(--space-4);
  background-color: var(--color-background-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--space-4);
  transition: box-shadow var(--transition-fast);
}

.model-card:hover {
  box-shadow: var(--shadow-md);
}

.model-info {
  flex: 1;
  min-width: 200px;
}

.model-name {
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--space-1);
}

.model-description {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-2);
}

.model-stats {
  display: flex;
  gap: var(--space-4);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.model-actions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* Admin dashboard */
.admin-dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-6);
  margin-bottom: var(--space-8);
}

.admin-card {
  background-color: var(--color-background-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: var(--space-4);
}

.admin-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--color-border);
}

.admin-card-title {
  font-weight: var(--font-weight-semibold);
}

.admin-card-actions {
  display: flex;
  gap: var(--space-2);
}

.admin-table-container {
  overflow-x: auto;
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
}

.admin-table th,
.admin-table td {
  padding: var(--space-3);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
}

.admin-table th {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  font-size: var(--font-size-sm);
  letter-spacing: 0.05em;
}

.admin-table tr:hover {
  background-color: var(--color-background-alt);
}

.admin-pagination {
  margin-top: var(--space-4);
  display: flex;
  justify-content: flex-end;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .container {
    padding-left: var(--space-4);
    padding-right: var(--space-4);
  }
  
  .section-title {
    font-size: var(--font-size-2xl);
  }
  
  .section-subtitle {
    font-size: var(--font-size-base);
  }
  
  .features-grid,
  .testimonials-grid,
  .admin-dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .hero-buttons,
  .cta .btn {
    width: 100%;
  }
  
  .topic-filters,
  .leaderboard-filters {
    flex-direction: column;
    gap: var(--space-3);
  }
  
  .topic-search,
  .filter-group {
    width: 100%;
    margin-right: 0;
  }
}

/* Print styles */
@media print {
  .site-header,
  .site-footer,
  .auth-buttons,
  .topic-filters,
  .leaderboard-filters,
  .profile-actions,
  .settings-sidebar {
    display: none !important;
  }
  
  body {
    background-color: white;
    color: black;
  }
  
  .container {
    max-width: 100%;
    padding: 0;
  }
  
  .card,
  .topic-item,
  .ai-response,
  .profile-header,
  .settings-content {
    box-shadow: none !important;
    border: 1px solid #ddd;
  }
  
  a {
    text-decoration: underline;
    color: black;
  }
  
  .page-break {
    page-break-before: always;
  }
}