/**
 * Huval Design System - Typography
 * 
 * This file contains all the typography-related styles and utility classes
 * for the Huval platform. It builds upon the variables defined in variables.css.
 */

/* Import variables */
@import 'variables.css';

/* Base typography */
html {
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
}

/* Headings */
h1, .h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-top: var(--space-8);
  margin-bottom: var(--space-4);
}

h2, .h2 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-top: var(--space-6);
  margin-bottom: var(--space-3);
}

h3, .h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-top: var(--space-5);
  margin-bottom: var(--space-2);
}

h4, .h4 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-top: var(--space-4);
  margin-bottom: var(--space-2);
}

h5, .h5 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-top: var(--space-3);
  margin-bottom: var(--space-1);
}

h6, .h6 {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-top: var(--space-3);
  margin-bottom: var(--space-1);
}

/* Responsive headings */
@media (max-width: 768px) {
  h1, .h1 {
    font-size: var(--font-size-3xl);
  }
  
  h2, .h2 {
    font-size: var(--font-size-2xl);
  }
  
  h3, .h3 {
    font-size: var(--font-size-xl);
  }
}

/* Paragraphs */
p {
  margin-top: var(--space-3);
  margin-bottom: var(--space-3);
}

.lead {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
}

.small {
  font-size: var(--font-size-sm);
}

.tiny {
  font-size: var(--font-size-xs);
}

/* Links */
a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-dark);
  text-decoration: underline;
}

/* Font weight utilities */
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

/* Font size utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }
.text-6xl { font-size: var(--font-size-6xl); }

/* Line height utilities */
.leading-none { line-height: var(--line-height-none); }
.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

/* Text alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* Text transformation */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* Text decoration */
.underline { text-decoration: underline; }
.line-through { text-decoration: line-through; }
.no-underline { text-decoration: none; }

/* Letter spacing */
.tracking-tighter { letter-spacing: -0.05em; }
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }

/* Font style */
.italic { font-style: italic; }
.not-italic { font-style: normal; }

/* Lists */
ul, ol {
  margin-top: var(--space-3);
  margin-bottom: var(--space-3);
  padding-left: var(--space-6);
}

ul {
  list-style-type: disc;
}

ol {
  list-style-type: decimal;
}

li {
  margin-top: var(--space-1);
  margin-bottom: var(--space-1);
}

/* Blockquotes */
blockquote {
  border-left: 4px solid var(--color-primary);
  padding-left: var(--space-4);
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: var(--color-text-secondary);
}

/* Code */
code {
  font-family: var(--font-family-mono);
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  background-color: var(--color-neutral-100);
  border-radius: var(--radius-md);
}

pre {
  font-family: var(--font-family-mono);
  background-color: var(--color-neutral-100);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  overflow-x: auto;
  margin-top: var(--space-4);
  margin-bottom: var(--space-4);
}

pre code {
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  font-size: var(--font-size-sm);
}

/* Truncate text */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Word break */
.break-normal { overflow-wrap: normal; word-break: normal; }
.break-words { overflow-wrap: break-word; }
.break-all { word-break: break-all; }

/* Whitespace */
.whitespace-normal { white-space: normal; }
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

/* Text overflow */
.overflow-ellipsis { text-overflow: ellipsis; }
.overflow-clip { text-overflow: clip; }

/* Vertical alignment */
.align-baseline { vertical-align: baseline; }
.align-top { vertical-align: top; }
.align-middle { vertical-align: middle; }
.align-bottom { vertical-align: bottom; }
.align-text-top { vertical-align: text-top; }
.align-text-bottom { vertical-align: text-bottom; }

/* Special text styles */
.text-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.text-gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.text-gradient-blue-purple {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-tertiary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}