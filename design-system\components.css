/**
 * Huval Design System - Components
 * 
 * This file contains styles for reusable components across the Huval platform.
 * It builds upon the variables defined in variables.css.
 */

/* Import variables */
@import 'variables.css';
@import 'colors.css';
@import 'typography.css';

/* Container */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: var(--container-2xl);
  }
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--button-padding-y) var(--button-padding-x);
  border-radius: var(--button-border-radius);
  font-weight: var(--font-weight-medium);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  line-height: 1.5;
  border: 1px solid transparent;
}

.btn:focus {
  outline: none;
}

.btn:disabled {
  opacity: 0.65;
  pointer-events: none;
}

/* Button variants */
.btn-primary {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
  border-color: var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.btn-primary:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

.btn-secondary {
  background-color: var(--color-secondary);
  color: var(--color-text-inverse);
  border-color: var(--color-secondary);
}

.btn-secondary:hover {
  background-color: var(--color-secondary-dark);
  border-color: var(--color-secondary-dark);
}

.btn-secondary:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.5);
}

.btn-tertiary {
  background-color: var(--color-tertiary);
  color: var(--color-text-inverse);
  border-color: var(--color-tertiary);
}

.btn-tertiary:hover {
  background-color: var(--color-tertiary-dark);
  border-color: var(--color-tertiary-dark);
}

.btn-tertiary:focus {
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.5);
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-outline-primary:hover {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.btn-outline-secondary {
  background-color: transparent;
  color: var(--color-secondary);
  border-color: var(--color-secondary);
}

.btn-outline-secondary:hover {
  background-color: var(--color-secondary);
  color: var(--color-text-inverse);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-text);
  border-color: transparent;
}

.btn-ghost:hover {
  background-color: var(--color-neutral-100);
}

/* Button sizes */
.btn-sm {
  padding: calc(var(--button-padding-y) * 0.75) calc(var(--button-padding-x) * 0.75);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: calc(var(--button-padding-y) * 1.25) calc(var(--button-padding-x) * 1.25);
  font-size: var(--font-size-lg);
}

/* Button with icon */
.btn-icon {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.btn-icon-only {
  padding: var(--button-padding-y);
  aspect-ratio: 1 / 1;
}

/* Cards */
.card {
  background-color: var(--color-background-card);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  padding: var(--card-padding);
  transition: box-shadow var(--transition-normal), transform var(--transition-normal);
}

.card:hover {
  box-shadow: var(--card-hover-shadow);
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  transform: translateY(-4px);
}

.card-header {
  margin-bottom: var(--space-4);
}

.card-footer {
  margin-top: var(--space-4);
  padding-top: var(--space-4);
  border-top: 1px solid var(--color-border);
}

/* Card variants */
.card-primary {
  border-top: 4px solid var(--color-primary);
}

.card-secondary {
  border-top: 4px solid var(--color-secondary);
}

.card-tertiary {
  border-top: 4px solid var(--color-tertiary);
}

/* Form elements */
.form-group {
  margin-bottom: var(--space-4);
}

.form-label {
  display: block;
  margin-bottom: var(--space-2);
  font-weight: var(--font-weight-medium);
  color: var(--color-text);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--input-padding-y) var(--input-padding-x);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background-card);
  background-clip: padding-box;
  border: 1px solid var(--input-border-color);
  border-radius: var(--input-border-radius);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
  border-color: var(--input-focus-border-color);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

.form-control::placeholder {
  color: var(--color-text-tertiary);
  opacity: 1;
}

.form-control:disabled {
  background-color: var(--color-neutral-100);
  opacity: 1;
  cursor: not-allowed;
}

.form-text {
  margin-top: var(--space-1);
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Checkboxes and radios */
.form-check {
  display: flex;
  align-items: center;
  margin-bottom: var(--space-2);
}

.form-check-input {
  margin-right: var(--space-2);
  width: 1rem;
  height: 1rem;
}

.form-check-label {
  margin-bottom: 0;
}

/* Custom checkbox */
.custom-checkbox {
  position: relative;
  padding-left: 1.75rem;
  cursor: pointer;
  user-select: none;
}

.custom-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 1.25rem;
  width: 1.25rem;
  background-color: var(--color-background-card);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.custom-checkbox:hover input ~ .checkmark {
  border-color: var(--color-primary);
}

.custom-checkbox input:checked ~ .checkmark {
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

.custom-checkbox input:checked ~ .checkmark:after {
  display: block;
}

.custom-checkbox .checkmark:after {
  left: 0.45rem;
  top: 0.25rem;
  width: 0.25rem;
  height: 0.5rem;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Badges */
.badge {
  display: inline-block;
  padding: 0.25em 0.5em;
  font-size: 0.75em;
  font-weight: var(--font-weight-semibold);
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--radius-full);
}

.badge-primary {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.badge-secondary {
  background-color: var(--color-secondary);
  color: var(--color-text-inverse);
}

.badge-tertiary {
  background-color: var(--color-tertiary);
  color: var(--color-text-inverse);
}

.badge-success {
  background-color: var(--color-success);
  color: var(--color-text-inverse);
}

.badge-warning {
  background-color: var(--color-warning);
  color: var(--color-text-inverse);
}

.badge-error {
  background-color: var(--color-error);
  color: var(--color-text-inverse);
}

.badge-outline {
  background-color: transparent;
  border: 1px solid currentColor;
}

.badge-outline-primary {
  color: var(--color-primary);
  border-color: var(--color-primary);
}

.badge-outline-secondary {
  color: var(--color-secondary);
  border-color: var(--color-secondary);
}

/* Alerts */
.alert {
  position: relative;
  padding: var(--space-4);
  margin-bottom: var(--space-4);
  border-radius: var(--radius-md);
  border-left: 4px solid transparent;
}

.alert-success {
  background-color: rgba(16, 185, 129, 0.1);
  border-left-color: var(--color-success);
  color: var(--color-success);
}

.alert-warning {
  background-color: rgba(245, 158, 11, 0.1);
  border-left-color: var(--color-warning);
  color: var(--color-warning);
}

.alert-error {
  background-color: rgba(239, 68, 68, 0.1);
  border-left-color: var(--color-error);
  color: var(--color-error);
}

.alert-info {
  background-color: rgba(59, 130, 246, 0.1);
  border-left-color: var(--color-info);
  color: var(--color-info);
}

/* Avatars */
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  background-color: var(--color-neutral-200);
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
  overflow: hidden;
}

.avatar-sm {
  width: 2rem;
  height: 2rem;
  font-size: var(--font-size-xs);
}

.avatar-md {
  width: 3rem;
  height: 3rem;
  font-size: var(--font-size-sm);
}

.avatar-lg {
  width: 4rem;
  height: 4rem;
  font-size: var(--font-size-base);
}

.avatar-xl {
  width: 6rem;
  height: 6rem;
  font-size: var(--font-size-lg);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-group {
  display: flex;
}

.avatar-group .avatar {
  margin-left: -0.5rem;
  border: 2px solid var(--color-background-card);
}

.avatar-group .avatar:first-child {
  margin-left: 0;
}

/* Tooltips */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-text {
  visibility: hidden;
  width: max-content;
  max-width: 200px;
  background-color: var(--color-neutral-800);
  color: var(--color-text-inverse);
  text-align: center;
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-3);
  position: absolute;
  z-index: var(--z-index-30);
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity var(--transition-fast);
  font-size: var(--font-size-sm);
  pointer-events: none;
}

.tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--color-neutral-800) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Tabs */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border);
  margin-bottom: var(--space-4);
}

.tab {
  padding: var(--space-3) var(--space-4);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  margin-bottom: -1px;
  color: var(--color-text-secondary);
  transition: all var(--transition-fast);
}

.tab:hover {
  color: var(--color-text);
}

.tab.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

.tab-content {
  padding: var(--space-4) 0;
}

.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* Pagination */
.pagination {
  display: flex;
  list-style: none;
  padding: 0;
  margin: var(--space-4) 0;
}

.page-item {
  margin: 0 var(--space-1);
}

.page-link {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  padding: 0 var(--space-2);
  border-radius: var(--radius-md);
  background-color: var(--color-background-card);
  color: var(--color-text);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.page-link:hover {
  background-color: var(--color-neutral-100);
  text-decoration: none;
}

.page-item.active .page-link {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

.page-item.disabled .page-link {
  color: var(--color-text-tertiary);
  pointer-events: none;
  cursor: not-allowed;
}

/* Breadcrumbs */
.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: var(--space-2) 0;
  margin: 0;
  list-style: none;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: var(--space-2);
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: var(--space-2);
  color: var(--color-text-tertiary);
  content: "/";
}

.breadcrumb-item a {
  color: var(--color-primary);
  text-decoration: none;
}

.breadcrumb-item.active {
  color: var(--color-text-secondary);
}

/* Progress */
.progress {
  display: flex;
  height: 0.5rem;
  overflow: hidden;
  background-color: var(--color-neutral-200);
  border-radius: var(--radius-full);
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: var(--color-text-inverse);
  text-align: center;
  white-space: nowrap;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
}

/* Spinners */
.spinner {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  vertical-align: text-bottom;
  border: 0.2em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  animation: spinner 0.75s linear infinite;
}

.spinner-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.15em;
}

.spinner-lg {
  width: 2.5rem;
  height: 2.5rem;
  border-width: 0.25em;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

/* Modals */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: var(--z-index-40);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--transition-normal), visibility var(--transition-normal);
}

.modal-backdrop.show {
  opacity: 1;
  visibility: visible;
}

.modal {
  background-color: var(--color-background-card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  z-index: var(--z-index-50);
  transform: translateY(20px);
  opacity: 0;
  transition: transform var(--transition-normal), opacity var(--transition-normal);
}

.modal-backdrop.show .modal {
  transform: translateY(0);
  opacity: 1;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-6);
  border-bottom: 1px solid var(--color-border);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.modal-close {
  background: transparent;
  border: none;
  font-size: var(--font-size-xl);
  cursor: pointer;
  color: var(--color-text-secondary);
  transition: color var(--transition-fast);
}

.modal-close:hover {
  color: var(--color-text);
}

.modal-body {
  padding: var(--space-6);
}

.modal-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--space-3);
}

/* Dropdowns */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  cursor: pointer;
}

.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: var(--z-index-20);
  display: none;
  min-width: 10rem;
  padding: var(--space-2) 0;
  margin: var(--space-1) 0 0;
  background-color: var(--color-background-card);
  background-clip: padding-box;
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--space-2) var(--space-4);
  clear: both;
  font-weight: var(--font-weight-normal);
  color: var(--color-text);
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  text-decoration: none;
  cursor: pointer;
}

.dropdown-item:hover, .dropdown-item:focus {
  color: var(--color-text);
  text-decoration: none;
  background-color: var(--color-neutral-100);
}

.dropdown-item.active, .dropdown-item:active {
  color: var(--color-text-inverse);
  text-decoration: none;
  background-color: var(--color-primary);
}

.dropdown-divider {
  height: 0;
  margin: var(--space-2) 0;
  overflow: hidden;
  border-top: 1px solid var(--color-border);
}

/* Toasts */
.toast-container {
  position: fixed;
  bottom: var(--space-4);
  right: var(--space-4);
  z-index: var(--z-index-50);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.toast {
  width: 350px;
  background-color: var(--color-background-card);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  display: flex;
  opacity: 0;
  transform: translateX(100%);
  transition: all var(--transition-normal);
}

.toast.show {
  opacity: 1;
  transform: translateX(0);
}

.toast-icon {
  width: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-inverse);
}

.toast-success .toast-icon {
  background-color: var(--color-success);
}

.toast-warning .toast-icon {
  background-color: var(--color-warning);
}

.toast-error .toast-icon {
  background-color: var(--color-error);
}

.toast-info .toast-icon {
  background-color: var(--color-info);
}

.toast-content {
  padding: var(--space-3);
  flex: 1;
}

.toast-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-1);
}

.toast-title {
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.toast-close {
  background: transparent;
  border: none;
  font-size: var(--font-size-lg);
  cursor: pointer;
  color: var(--color-text-secondary);
  transition: color var(--transition-fast);
}

.toast-close:hover {
  color: var(--color-text);
}

.toast-body {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* BentoUI Grid */
.bento-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--bento-gap);
}

.bento-item {
  border-radius: var(--bento-border-radius);
  overflow: hidden;
  background-color: var(--color-background-card);
  box-shadow: var(--card-shadow);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.bento-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--card-hover-shadow);
}

.bento-item-featured {
  grid-column: span 2;
  grid-row: span 2;
}

@media (max-width: 768px) {
  .bento-item-featured {
    grid-column: span 1;
    grid-row: span 1;
  }
}

.bento-content {
  padding: var(--space-4);
}

/* Voting component */
.vote-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: var(--space-4);
}

.vote-button {
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--color-text-secondary);
  transition: color var(--transition-fast);
  font-size: var(--font-size-lg);
}

.vote-button:hover {
  color: var(--color-primary);
}

.vote-button.upvoted {
  color: var(--color-primary);
}

.vote-button.downvoted {
  color: var(--color-error);
}

.vote-count {
  font-weight: var(--font-weight-semibold);
  margin: var(--space-1) 0;
}

/* Topic card */
.topic-card {
  display: flex;
  padding: var(--space-4);
  border-bottom: 1px solid var(--color-border);
}

.topic-content {
  flex: 1;
}

.topic-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-2);
}

.topic-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

.topic-meta {
  display: flex;
  gap: var(--space-3);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
  margin-bottom: var(--space-2);
}

.topic-category {
  display: inline-block;
}

.topic-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--space-3);
}

.topic-stats {
  display: flex;
  gap: var(--space-3);
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

/* AI Response card */
.ai-response {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  background-color: var(--color-background-alt);
  margin-bottom: var(--space-4);
}

.ai-response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-3);
}

.ai-provider {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.ai-provider-logo {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: var(--radius-full);
  background-color: var(--color-neutral-200);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.ai-provider-name {
  font-weight: var(--font-weight-semibold);
}

.ai-response-content {
  margin-bottom: var(--space-3);
}

.ai-response-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-response-actions {
  display: flex;
  gap: var(--space-2);
}

/* Leaderboard component */
.leaderboard {
  width: 100%;
  border-collapse: collapse;
}

.leaderboard th,
.leaderboard td {
  padding: var(--space-3);
  text-align: left;
  border-bottom: 1px solid var(--color-border);
}

.leaderboard th {
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  font-size: var(--font-size-sm);
  letter-spacing: 0.05em;
}

.leaderboard tr:hover {
  background-color: var(--color-background-alt);
}

.leaderboard-rank {
  font-weight: var(--font-weight-bold);
  width: 3rem;
  text-align: center;
}

.leaderboard-model {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.leaderboard-model-logo {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-full);
  background-color: var(--color-neutral-200);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.leaderboard-score {
  font-weight: var(--font-weight-semibold);
}

.leaderboard-top .leaderboard-rank {
  color: var(--color-primary);
}

/* Category pills */
.category-pill {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  background-color: var(--color-neutral-100);
  color: var(--color-text-secondary);
  transition: all var(--transition-fast);
}

.category-pill:hover {
  background-color: var(--color-neutral-200);
  color: var(--color-text);
  text-decoration: none;
}

.category-pill.active {
  background-color: var(--color-primary);
  color: var(--color-text-inverse);
}

/* NSFW tag */
.nsfw-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.15rem 0.5rem;
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  background-color: var(--color-error);
  color: var(--color-text-inverse);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* User profile card */
.profile-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-6);
  text-align: center;
}

.profile-avatar {
  width: 6rem;
  height: 6rem;
  border-radius: var(--radius-full);
  margin-bottom: var(--space-4);
  background-color: var(--color-neutral-200);
  overflow: hidden;
}

.profile-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--space-1);
}

.profile-username {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-4);
}

.profile-role {
  margin-bottom: var(--space-4);
}

.profile-stats {
  display: flex;
  gap: var(--space-6);
  margin-bottom: var(--space-4);
}

.profile-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.profile-stat-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
}

.profile-stat-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* Utility classes */
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }
.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }
.align-items-center { align-items: center; }
.align-items-baseline { align-items: baseline; }
.align-items-stretch { align-items: stretch; }
.align-self-start { align-self: flex-start; }
.align-self-end { align-self: flex-end; }
.align-self-center { align-self: center; }
.align-self-baseline { align-self: baseline; }
.align-self-stretch { align-self: stretch; }
.flex-grow-0 { flex-grow: 0; }
.flex-grow-1 { flex-grow: 1; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-shrink-1 { flex-shrink: 1; }

.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }

.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-5 { margin-top: var(--space-5); }
.mt-6 { margin-top: var(--space-6); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--space-1); }
.mr-2 { margin-right: var(--space-2); }
.mr-3 { margin-right: var(--space-3); }
.mr-4 { margin-right: var(--space-4); }
.mr-5 { margin-right: var(--space-5); }
.mr-6 { margin-right: var(--space-6); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-5 { margin-bottom: var(--space-5); }
.mb-6 { margin-bottom: var(--space-6); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--space-1); }
.ml-2 { margin-left: var(--space-2); }
.ml-3 { margin-left: var(--space-3); }
.ml-4 { margin-left: var(--space-4); }
.ml-5 { margin-left: var(--space-5); }
.ml-6 { margin-left: var(--space-6); }

.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: var(--space-1); margin-right: var(--space-1); }
.mx-2 { margin-left: var(--space-2); margin-right: var(--space-2); }
.mx-3 { margin-left: var(--space-3); margin-right: var(--space-3); }
.mx-4 { margin-left: var(--space-4); margin-right: var(--space-4); }
.mx-5 { margin-left: var(--space-5); margin-right: var(--space-5); }
.mx-6 { margin-left: var(--space-6); margin-right: var(--space-6); }
.mx-auto { margin-left: auto; margin-right: auto; }

.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: var(--space-1); margin-bottom: var(--space-1); }
.my-2 { margin-top: var(--space-2); margin-bottom: var(--space-2); }
.my-3 { margin-top: var(--space-3); margin-bottom: var(--space-3); }
.my-4 { margin-top: var(--space-4); margin-bottom: var(--space-4); }
.my-5 { margin-top: var(--space-5); margin-bottom: var(--space-5); }
.my-6 { margin-top: var(--space-6); margin-bottom: var(--space-6); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--space-1); }
.pt-2 { padding-top: var(--space-2); }
.pt-3 { padding-top: var(--space-3); }
.pt-4 { padding-top: var(--space-4); }
.pt-5 { padding-top: var(--space-5); }
.pt-6 { padding-top: var(--space-6); }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: var(--space-1); }
.pr-2 { padding-right: var(--space-2); }
.pr-3 { padding-right: var(--space-3); }
.pr-4 { padding-right: var(--space-4); }
.pr-5 { padding-right: var(--space-5); }
.pr-6 { padding-right: var(--space-6); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--space-1); }
.pb-2 { padding-bottom: var(--space-2); }
.pb-3 { padding-bottom: var(--space-3); }
.pb-4 { padding-bottom: var(--space-4); }
.pb-5 { padding-bottom: var(--space-5); }
.pb-6 { padding-bottom: var(--space-6); }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: var(--space-1); }
.pl-2 { padding-left: var(--space-2); }
.pl-3 { padding-left: var(--space-3); }
.pl-4 { padding-left: var(--space-4); }
.pl-5 { padding-left: var(--space-5); }
.pl-6 { padding-left: var(--space-6); }

.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-5 { padding-left: var(--space-5); padding-right: var(--space-5); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }
.py-5 { padding-top: var(--space-5); padding-bottom: var(--space-5); }
.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }

.w-25 { width: 25%; }
.w-50 { width: 50%; }
.w-75 { width: 75%; }
.w-100 { width: 100%; }
.w-auto { width: auto; }

.h-25 { height: 25%; }
.h-50 { height: 50%; }
.h-75 { height: 75%; }
.h-100 { height: 100%; }
.h-auto { height: auto; }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: var(--radius-3xl); }
.rounded-full { border-radius: var(--radius-full); }

.border { border: 1px solid var(--color-border); }
.border-0 { border: 0; }
.border-t { border-top: 1px solid var(--color-border); }
.border-r { border-right: 1px solid var(--color-border); }
.border-b { border-bottom: 1px solid var(--color-border); }
.border-l { border-left: 1px solid var(--color-border); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-none { box-shadow: none; }

.position-static { position: static; }
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

.z-0 { z-index: var(--z-index-0); }
.z-10 { z-index: var(--z-index-10); }
.z-20 { z-index: var(--z-index-20); }
.z-30 { z-index: var(--z-index-30); }
.z-40 { z-index: var(--z-index-40); }
.z-50 { z-index: var(--z-index-50); }
.z-auto { z-index: var(--z-index-auto); }

.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

.d-none { display: none; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-block { display: block; }
.d-grid { display: grid; }

.visible { visibility: visible; }
.invisible { visibility: hidden; }

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .d-sm-none { display: none; }
  .d-sm-block { display: block; }
  .d-sm-flex { display: flex; }
}

@media (max-width: 768px) {
  .d-md-none { display: none; }
  .d-md-block { display: block; }
  .d-md-flex { display: flex; }
}

@media (max-width: 1024px) {
  .d-lg-none { display: none; }
  .d-lg-block { display: block; }
  .d-lg-flex { display: flex; }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn var(--transition-normal) ease-in-out;
}

.animate-slide-up {
  animation: slideInUp var(--transition-normal) ease-in-out;
}

.animate-slide-right {
  animation: slideInRight var(--transition-normal) ease-in-out;
}