/**
 * Huval Design System - Variables
 * 
 * This file contains all the CSS variables used throughout the Huval platform.
 * These variables ensure consistency in design and make it easier to update
 * the design system in the future.
 */

:root {
  /* Color System - Dark Theme Inspired */
  --color-primary: #10B981; /* Green - Primary accent */
  --color-primary-light: #34D399;
  --color-primary-dark: #059669;

  --color-secondary: #3B82F6; /* Blue - Secondary */
  --color-secondary-light: #60A5FA;
  --color-secondary-dark: #2563EB;

  --color-tertiary: #8B5CF6; /* Purple - Tertiary */
  --color-tertiary-light: #A78BFA;
  --color-tertiary-dark: #7C3AED;

  /* Dark Theme Neutrals */
  --color-neutral-50: #FAFAFA;
  --color-neutral-100: #F5F5F5;
  --color-neutral-200: #E5E5E5;
  --color-neutral-300: #D4D4D4;
  --color-neutral-400: #A3A3A3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;
  --color-neutral-950: #0A0A0A;

  /* Dark Background System */
  --color-background: #0A0A0A;
  --color-background-secondary: #171717;
  --color-background-tertiary: #262626;
  --color-surface: #FFFFFF;
  --color-surface-secondary: #FAFAFA;

  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: #3B82F6;
  
  /* Background Colors - Dark Theme Default */
  --color-background: var(--color-background);
  --color-background-alt: var(--color-background-secondary);
  --color-background-card: var(--color-surface);

  /* Text Colors - Dark Theme Default */
  --color-text: var(--color-text);
  --color-text-secondary: var(--color-text-secondary);
  --color-text-tertiary: var(--color-text-muted);
  --color-text-inverse: var(--color-text-inverse);
  --color-text-on-surface: var(--color-text-on-surface);

  /* Border Colors */
  --color-border: var(--color-neutral-700);
  --color-border-focus: var(--color-primary);
  --color-border-light: var(--color-neutral-600);
  
  /* Spacing System */
  --space-1: 0.25rem; /* 4px */
  --space-2: 0.5rem;  /* 8px */
  --space-3: 0.75rem; /* 12px */
  --space-4: 1rem;    /* 16px */
  --space-5: 1.25rem; /* 20px */
  --space-6: 1.5rem;  /* 24px */
  --space-8: 2rem;    /* 32px */
  --space-10: 2.5rem; /* 40px */
  --space-12: 3rem;   /* 48px */
  --space-16: 4rem;   /* 64px */
  --space-20: 5rem;   /* 80px */
  --space-24: 6rem;   /* 96px */
  --space-32: 8rem;   /* 128px */
  --space-40: 10rem;  /* 160px */
  --space-48: 12rem;  /* 192px */
  --space-56: 14rem;  /* 224px */
  --space-64: 16rem;  /* 256px */
  
  /* Typography */
  --font-family-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  --font-family-mono: 'JetBrains Mono', Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  --font-size-5xl: 3rem;     /* 48px */
  --font-size-6xl: 3.75rem;  /* 60px */
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Border Radius - Increased for modern look */
  --radius-sm: 0.25rem;  /* 4px */
  --radius-md: 0.5rem;   /* 8px */
  --radius-lg: 0.75rem;  /* 12px */
  --radius-xl: 1rem;     /* 16px */
  --radius-2xl: 1.25rem; /* 20px */
  --radius-3xl: 1.5rem;  /* 24px */
  --radius-full: 9999px;
  
  /* Shadows - Enhanced for dark theme */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);
  --shadow-glow: 0 0 20px rgba(16, 185, 129, 0.15);
  
  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
  
  /* Z-index */
  --z-index-0: 0;
  --z-index-10: 10;
  --z-index-20: 20;
  --z-index-30: 30;
  --z-index-40: 40;
  --z-index-50: 50;
  --z-index-auto: auto;
  
  /* Container widths */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  
  /* Card specific - Enhanced for modern look */
  --card-padding: var(--space-6);
  --card-border-radius: var(--radius-xl);
  --card-shadow: var(--shadow-lg);
  --card-hover-shadow: var(--shadow-xl);
  --card-background: var(--color-surface);
  --card-border: 1px solid var(--color-border-light);

  /* Button specific - Enhanced styling */
  --button-padding-x: var(--space-6);
  --button-padding-y: var(--space-3);
  --button-border-radius: var(--radius-lg);
  --button-font-weight: var(--font-weight-medium);
  
  /* Input specific */
  --input-padding-x: var(--space-4);
  --input-padding-y: var(--space-2);
  --input-border-radius: var(--radius-md);
  --input-border-color: var(--color-border);
  --input-focus-border-color: var(--color-border-focus);
  
  /* Header specific */
  --header-height: 4rem;
  
  /* Footer specific */
  --footer-padding-y: var(--space-8);
  
  /* Navigation specific */
  --nav-item-padding-x: var(--space-4);
  --nav-item-padding-y: var(--space-2);
  
  /* BentoUI grid specific */
  --bento-gap: var(--space-4);
  --bento-border-radius: var(--radius-lg);
}

/* Gradient utilities for modern design */
:root {
  --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
  --gradient-tertiary: linear-gradient(135deg, var(--color-tertiary) 0%, var(--color-tertiary-dark) 100%);
  --gradient-surface: linear-gradient(135deg, var(--color-surface) 0%, var(--color-surface-secondary) 100%);
  --gradient-dark: linear-gradient(135deg, var(--color-neutral-800) 0%, var(--color-neutral-900) 100%);
}