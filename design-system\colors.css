/**
 * Huval Design System - Colors
 * 
 * This file contains all the color-related styles and utility classes
 * for the Huval platform. It builds upon the variables defined in variables.css.
 */

/* Import variables */
@import 'variables.css';

/* Background color utilities */
.bg-primary { background-color: var(--color-primary); }
.bg-primary-light { background-color: var(--color-primary-light); }
.bg-primary-dark { background-color: var(--color-primary-dark); }

.bg-secondary { background-color: var(--color-secondary); }
.bg-secondary-light { background-color: var(--color-secondary-light); }
.bg-secondary-dark { background-color: var(--color-secondary-dark); }

.bg-tertiary { background-color: var(--color-tertiary); }
.bg-tertiary-light { background-color: var(--color-tertiary-light); }
.bg-tertiary-dark { background-color: var(--color-tertiary-dark); }

.bg-neutral-50 { background-color: var(--color-neutral-50); }
.bg-neutral-100 { background-color: var(--color-neutral-100); }
.bg-neutral-200 { background-color: var(--color-neutral-200); }
.bg-neutral-300 { background-color: var(--color-neutral-300); }
.bg-neutral-400 { background-color: var(--color-neutral-400); }
.bg-neutral-500 { background-color: var(--color-neutral-500); }
.bg-neutral-600 { background-color: var(--color-neutral-600); }
.bg-neutral-700 { background-color: var(--color-neutral-700); }
.bg-neutral-800 { background-color: var(--color-neutral-800); }
.bg-neutral-900 { background-color: var(--color-neutral-900); }

.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-error { background-color: var(--color-error); }
.bg-info { background-color: var(--color-info); }

.bg-white { background-color: #FFFFFF; }
.bg-black { background-color: #000000; }

.bg-transparent { background-color: transparent; }

/* Background gradients */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-secondary-dark) 100%);
}

.bg-gradient-tertiary {
  background: linear-gradient(135deg, var(--color-tertiary) 0%, var(--color-tertiary-dark) 100%);
}

.bg-gradient-blue-purple {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-tertiary) 100%);
}

.bg-gradient-green-blue {
  background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-primary) 100%);
}

/* Text color utilities */
.text-primary { color: var(--color-primary); }
.text-primary-light { color: var(--color-primary-light); }
.text-primary-dark { color: var(--color-primary-dark); }

.text-secondary { color: var(--color-secondary); }
.text-secondary-light { color: var(--color-secondary-light); }
.text-secondary-dark { color: var(--color-secondary-dark); }

.text-tertiary { color: var(--color-tertiary); }
.text-tertiary-light { color: var(--color-tertiary-light); }
.text-tertiary-dark { color: var(--color-tertiary-dark); }

.text-neutral-50 { color: var(--color-neutral-50); }
.text-neutral-100 { color: var(--color-neutral-100); }
.text-neutral-200 { color: var(--color-neutral-200); }
.text-neutral-300 { color: var(--color-neutral-300); }
.text-neutral-400 { color: var(--color-neutral-400); }
.text-neutral-500 { color: var(--color-neutral-500); }
.text-neutral-600 { color: var(--color-neutral-600); }
.text-neutral-700 { color: var(--color-neutral-700); }
.text-neutral-800 { color: var(--color-neutral-800); }
.text-neutral-900 { color: var(--color-neutral-900); }

.text-success { color: var(--color-success); }
.text-warning { color: var(--color-warning); }
.text-error { color: var(--color-error); }
.text-info { color: var(--color-info); }

.text-white { color: #FFFFFF; }
.text-black { color: #000000; }

/* Border color utilities */
.border-primary { border-color: var(--color-primary); }
.border-primary-light { border-color: var(--color-primary-light); }
.border-primary-dark { border-color: var(--color-primary-dark); }

.border-secondary { border-color: var(--color-secondary); }
.border-secondary-light { border-color: var(--color-secondary-light); }
.border-secondary-dark { border-color: var(--color-secondary-dark); }

.border-tertiary { border-color: var(--color-tertiary); }
.border-tertiary-light { border-color: var(--color-tertiary-light); }
.border-tertiary-dark { border-color: var(--color-tertiary-dark); }

.border-neutral-50 { border-color: var(--color-neutral-50); }
.border-neutral-100 { border-color: var(--color-neutral-100); }
.border-neutral-200 { border-color: var(--color-neutral-200); }
.border-neutral-300 { border-color: var(--color-neutral-300); }
.border-neutral-400 { border-color: var(--color-neutral-400); }
.border-neutral-500 { border-color: var(--color-neutral-500); }
.border-neutral-600 { border-color: var(--color-neutral-600); }
.border-neutral-700 { border-color: var(--color-neutral-700); }
.border-neutral-800 { border-color: var(--color-neutral-800); }
.border-neutral-900 { border-color: var(--color-neutral-900); }

.border-success { border-color: var(--color-success); }
.border-warning { border-color: var(--color-warning); }
.border-error { border-color: var(--color-error); }
.border-info { border-color: var(--color-info); }

.border-white { border-color: #FFFFFF; }
.border-black { border-color: #000000; }

.border-transparent { border-color: transparent; }

/* Focus ring colors */
.ring-primary { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); }
.ring-secondary { box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.5); }
.ring-tertiary { box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.5); }
.ring-error { box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.5); }

/* Hover state utilities */
.hover-bg-primary:hover { background-color: var(--color-primary); }
.hover-bg-primary-light:hover { background-color: var(--color-primary-light); }
.hover-bg-primary-dark:hover { background-color: var(--color-primary-dark); }

.hover-bg-secondary:hover { background-color: var(--color-secondary); }
.hover-bg-secondary-light:hover { background-color: var(--color-secondary-light); }
.hover-bg-secondary-dark:hover { background-color: var(--color-secondary-dark); }

.hover-text-primary:hover { color: var(--color-primary); }
.hover-text-primary-light:hover { color: var(--color-primary-light); }
.hover-text-primary-dark:hover { color: var(--color-primary-dark); }

.hover-text-secondary:hover { color: var(--color-secondary); }
.hover-text-secondary-light:hover { color: var(--color-secondary-light); }
.hover-text-secondary-dark:hover { color: var(--color-secondary-dark); }

.hover-border-primary:hover { border-color: var(--color-primary); }
.hover-border-secondary:hover { border-color: var(--color-secondary); }

/* Active state utilities */
.active-bg-primary:active { background-color: var(--color-primary-dark); }
.active-bg-secondary:active { background-color: var(--color-secondary-dark); }

.active-text-primary:active { color: var(--color-primary-dark); }
.active-text-secondary:active { color: var(--color-secondary-dark); }

/* Focus state utilities */
.focus-ring-primary:focus { box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5); }
.focus-ring-secondary:focus { box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.5); }
.focus-ring-tertiary:focus { box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.5); }
.focus-ring-error:focus { box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.5); }

/* Opacity utilities */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* Transition utilities for colors */
.transition-colors {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-duration: var(--transition-normal);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}