Create a full-stack AI evaluation platform that follows forum principles. Users create topics, choose categories (coding, general knowledge, QA, etc.), indicate if their question is NSFW, and submit their topics. Using proper batching and queuing, topics are sent to multiple AI systems through APIs, and their responses are linked anonymously to questions as replies (similar to any forum or QA platform). Every registered user can vote on and evaluate the responses, with appropriate rate limits in place.

The platform should support multiple account types:

Super Admin: Full administrative rights including adding and editing AI providers and models, plus all configuration changes

Admin Support: Moderation and support capabilities

Simple Users: Can post topics, vote, and review responses and topics

AI Provider: Can list their AI models with API keys and expected request formats, view their AI's performance and evaluations (requires profile verification)

The platform should be built with comprehensive logic, considerations, and principles. It features a global leaderboard for AI models (LLMs) based on user evaluations. The platform includes a free public leaderboard, public index, and home page.

The design should be minimalist and modern with simple colors, utilizing BentoUI and card-inspired design principles along with all modern considerations and best practices. The design must adhere to current web design principles and standards (2025).

The platform name is `huval`. The goal is to address the shortcomings of existing AI evaluation platforms that rely on datasets and are influenced by model creators. Create a unique and creative design and UI for the platform.

The index and all pages should feature rich context, content, and sections. The home page (landing page) should include at least 7 sections, and the navigation must contain no more than 8 elements, including login and registration links/buttons.

The platform uses a forum-style approach to break away from the uninspiring, traditional, and monetarily biased existing evaluation platforms.

-----------

Proceed to etablishing the complete design system and the creation or highly customised pure html templates without any backend features